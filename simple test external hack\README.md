# Simple Counter App - Basic Security Level

## Description
This is a basic pygame application with minimal security measures. The counter value is stored in a simple variable that can be easily modified through memory editing tools or by directly modifying the Python code.

## Features
- Clickable button that increments a counter
- Counter starts at 0
- Simple GUI with pygame
- **Security Level: BASIC (Easy to hack)**

## How to Run
1. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

2. Run the application:
   ```
   python main.py
   ```

## Security Vulnerabilities (Intentional)
- Counter stored in a simple instance variable (`self.counter`)
- No validation or protection mechanisms
- Direct memory access possible
- Code is easily readable and modifiable
- No obfuscation or anti-tampering measures

## Potential Attack Vectors
- Memory editing tools (Cheat Engine, etc.)
- Direct code modification
- Python debugger manipulation
- Runtime variable modification

This application is designed to be easily hackable for educational purposes.
