#!/usr/bin/env python3
"""
Medium Counter Application - Moderate Security Level
A pygame application with basic security measures including validation,
checksums, and simple obfuscation.
"""

import pygame
import sys
import hashlib
import time
import random

# Initialize Pygame
pygame.init()

# Constants
WINDOW_WIDTH = 400
WINDOW_HEIGHT = 300
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
BLUE = (0, 100, 200)
LIGHT_BLUE = (100, 150, 255)
ORANGE = (255, 165, 0)
GRAY = (128, 128, 128)

class MediumCounterApp:
    def __init__(self):
        # Obfuscated variable names
        self._x7f2a = 0  # actual counter
        self._x9b1c = 0  # checksum counter
        self._x4d8e = time.time()  # last validation time
        self._x6f3a = random.randint(1000, 9999)  # validation key
        
        # Decoy variables to confuse memory scanners
        self.counter = 999999
        self.fake_counter = 123456
        self.decoy_value = 777777
        
        # Setup display
        self.screen = pygame.display.set_mode((WINDOW_WIDTH, WINDOW_HEIGHT))
        pygame.display.set_caption("Medium Counter - Moderate Security")
        
        # Font for text
        self.font = pygame.font.Font(None, 36)
        self.button_font = pygame.font.Font(None, 24)
        
        # Button properties
        self.button_rect = pygame.Rect(150, 200, 100, 50)
        self.button_color = BLUE
        self.button_hover_color = LIGHT_BLUE
        self.button_pressed = False
        
        # Clock for FPS
        self.clock = pygame.time.Clock()
        
        # Initialize checksums
        self._update_checksum()
        
    def _calculate_checksum(self, value):
        """Calculate a simple checksum for the counter value"""
        data = f"{value}_{self._x6f3a}_{int(self._x4d8e)}"
        return int(hashlib.md5(data.encode()).hexdigest()[:8], 16)
        
    def _update_checksum(self):
        """Update the checksum for the current counter value"""
        self._x9b1c = self._calculate_checksum(self._x7f2a)
        
    def _validate_counter(self):
        """Validate that the counter hasn't been tampered with"""
        expected_checksum = self._calculate_checksum(self._x7f2a)
        
        if self._x9b1c != expected_checksum:
            # Counter has been tampered with - reset to 0
            self._x7f2a = 0
            self._update_checksum()
            return False
            
        # Additional validation: check if counter is reasonable
        if self._x7f2a < 0 or self._x7f2a > 10000:
            self._x7f2a = 0
            self._update_checksum()
            return False
            
        return True
        
    def _get_real_counter(self):
        """Get the real counter value with validation"""
        current_time = time.time()
        
        # Validate every 2 seconds
        if current_time - self._x4d8e > 2.0:
            self._validate_counter()
            self._x4d8e = current_time
            
        return self._x7f2a
        
    def _increment_counter(self):
        """Safely increment the counter with validation"""
        if self._validate_counter():
            self._x7f2a += 1
            self._update_checksum()
            
            # Update decoy values to confuse attackers
            self.counter = random.randint(100000, 999999)
            self.fake_counter = random.randint(100000, 999999)
            self.decoy_value = random.randint(100000, 999999)
        
    def handle_events(self):
        """Handle pygame events"""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                return False
                
            elif event.type == pygame.MOUSEBUTTONDOWN:
                if event.button == 1:  # Left click
                    if self.button_rect.collidepoint(event.pos):
                        self.button_pressed = True
                        
            elif event.type == pygame.MOUSEBUTTONUP:
                if event.button == 1:  # Left click release
                    if self.button_pressed and self.button_rect.collidepoint(event.pos):
                        # Increment with validation
                        self._increment_counter()
                    self.button_pressed = False
                    
        return True
        
    def draw(self):
        """Draw the application"""
        # Clear screen
        self.screen.fill(WHITE)
        
        # Draw title
        title_text = self.font.render("Medium Counter App", True, BLACK)
        title_rect = title_text.get_rect(center=(WINDOW_WIDTH // 2, 50))
        self.screen.blit(title_text, title_rect)
        
        # Draw counter value (with validation)
        real_counter = self._get_real_counter()
        counter_text = self.font.render(f"Counter: {real_counter}", True, BLACK)
        counter_rect = counter_text.get_rect(center=(WINDOW_WIDTH // 2, 120))
        self.screen.blit(counter_text, counter_rect)
        
        # Draw button
        mouse_pos = pygame.mouse.get_pos()
        if self.button_rect.collidepoint(mouse_pos):
            button_color = self.button_hover_color
        else:
            button_color = self.button_color
            
        pygame.draw.rect(self.screen, button_color, self.button_rect)
        pygame.draw.rect(self.screen, BLACK, self.button_rect, 2)
        
        # Draw button text
        button_text = self.button_font.render("Click Me!", True, WHITE)
        button_text_rect = button_text.get_rect(center=self.button_rect.center)
        self.screen.blit(button_text, button_text_rect)
        
        # Draw security level indicator
        security_text = self.button_font.render("Security: MODERATE (Some protection)", True, ORANGE)
        self.screen.blit(security_text, (10, WINDOW_HEIGHT - 25))
        
        # Update display
        pygame.display.flip()
        
    def run(self):
        """Main game loop"""
        running = True
        while running:
            running = self.handle_events()
            self.draw()
            self.clock.tick(60)  # 60 FPS
            
        pygame.quit()
        sys.exit()

if __name__ == "__main__":
    app = MediumCounterApp()
    app.run()
