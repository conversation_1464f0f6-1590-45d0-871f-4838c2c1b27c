#!/usr/bin/env python3
"""
Simple Counter Application - Basic Security Level
A basic pygame application with a clickable button and counter.
This version has minimal security and is easy to hack.
"""

import pygame
import sys

# Initialize Pygame
pygame.init()

# Constants
WINDOW_WIDTH = 400
WINDOW_HEIGHT = 300
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
BLUE = (0, 100, 200)
LIGHT_BLUE = (100, 150, 255)
GRAY = (128, 128, 128)

class SimpleCounterApp:
    def __init__(self):
        # Basic counter variable - easily accessible and modifiable
        self.counter = 0
        
        # Setup display
        self.screen = pygame.display.set_mode((WINDOW_WIDTH, WINDOW_HEIGHT))
        pygame.display.set_caption("Simple Counter - Easy to Hack")
        
        # Font for text
        self.font = pygame.font.Font(None, 36)
        self.button_font = pygame.font.Font(None, 24)
        
        # Button properties
        self.button_rect = pygame.Rect(150, 200, 100, 50)
        self.button_color = BLUE
        self.button_hover_color = LIGHT_BLUE
        self.button_pressed = False
        
        # Clock for FPS
        self.clock = pygame.time.Clock()
        
    def handle_events(self):
        """Handle pygame events"""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                return False
                
            elif event.type == pygame.MOUSEBUTTONDOWN:
                if event.button == 1:  # Left click
                    if self.button_rect.collidepoint(event.pos):
                        self.button_pressed = True
                        
            elif event.type == pygame.MOUSEBUTTONUP:
                if event.button == 1:  # Left click release
                    if self.button_pressed and self.button_rect.collidepoint(event.pos):
                        # Simple increment - no validation or protection
                        self.counter += 1
                    self.button_pressed = False
                    
        return True
        
    def draw(self):
        """Draw the application"""
        # Clear screen
        self.screen.fill(WHITE)
        
        # Draw title
        title_text = self.font.render("Simple Counter App", True, BLACK)
        title_rect = title_text.get_rect(center=(WINDOW_WIDTH // 2, 50))
        self.screen.blit(title_text, title_rect)
        
        # Draw counter value
        counter_text = self.font.render(f"Counter: {self.counter}", True, BLACK)
        counter_rect = counter_text.get_rect(center=(WINDOW_WIDTH // 2, 120))
        self.screen.blit(counter_text, counter_rect)
        
        # Draw button
        mouse_pos = pygame.mouse.get_pos()
        if self.button_rect.collidepoint(mouse_pos):
            button_color = self.button_hover_color
        else:
            button_color = self.button_color
            
        pygame.draw.rect(self.screen, button_color, self.button_rect)
        pygame.draw.rect(self.screen, BLACK, self.button_rect, 2)
        
        # Draw button text
        button_text = self.button_font.render("Click Me!", True, WHITE)
        button_text_rect = button_text.get_rect(center=self.button_rect.center)
        self.screen.blit(button_text, button_text_rect)
        
        # Draw security level indicator
        security_text = self.button_font.render("Security: BASIC (Easy to hack)", True, GRAY)
        self.screen.blit(security_text, (10, WINDOW_HEIGHT - 25))
        
        # Update display
        pygame.display.flip()
        
    def run(self):
        """Main game loop"""
        running = True
        while running:
            running = self.handle_events()
            self.draw()
            self.clock.tick(60)  # 60 FPS
            
        pygame.quit()
        sys.exit()

if __name__ == "__main__":
    app = SimpleCounterApp()
    app.run()
